import os
os.environ.pop("HTTP_PROXY", None)
os.environ.pop("HTTPS_PROXY", None)
os.environ["NO_PROXY"] = "*"

import pandas as pd
import requests
import time
import random
import logging
import sys
import io
import traceback
from PIL import Image
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor, as_completed
from openpyxl.drawing.image import Image as XLImage
from openpyxl.utils import get_column_letter

# ========== 配置 ==========
input_file = r'D:\hubtest\获取店铺订单.xlsx'
timestamp = time.strftime("%Y%m%d_%H%M%S")
order_output_file = fr'D:\hubtest\ozon订单信息明细_{timestamp}.xlsx'
log_file = fr'D:\hubtest\ozon订单日志_{timestamp}.log'
use_proxy = False
retries = 3
MAX_WORKERS = 5
# 图片设置
IMAGE_WIDTH = 80  # 图片宽度
IMAGE_HEIGHT = 80  # 图片高度
# ==========================

# ========== 日志配置 ==========
import threading
import atexit

# 创建线程锁确保日志操作的线程安全
log_lock = threading.Lock()

# 创建专用的logger实例，避免与其他模块冲突
logger = logging.getLogger('ozon_order_tool')
logger.setLevel(logging.INFO)

# 清除可能存在的旧handlers
logger.handlers.clear()

# 创建文件处理器
file_handler = logging.FileHandler(log_file, encoding='utf-8-sig')
file_handler.setFormatter(logging.Formatter('[%(asctime)s] %(message)s'))

# 创建控制台处理器
console_handler = logging.StreamHandler(sys.stdout)
console_handler.setLevel(logging.INFO)
console_handler.setFormatter(logging.Formatter('[%(asctime)s] %(message)s'))

# 添加处理器
logger.addHandler(file_handler)
logger.addHandler(console_handler)

# 防止日志传播到根logger
logger.propagate = False

def log(msg):
    """线程安全的日志记录函数"""
    global file_handler  # 必须在函数开始就声明global
    with log_lock:
        try:
            # 检查文件处理器是否仍然有效
            if file_handler.stream and not file_handler.stream.closed:
                logger.info(msg)
            else:
                # 如果文件处理器已关闭，重新创建
                logger.removeHandler(file_handler)
                file_handler = logging.FileHandler(log_file, encoding='utf-8-sig', mode='a')
                file_handler.setFormatter(logging.Formatter('[%(asctime)s] %(message)s'))
                logger.addHandler(file_handler)
                logger.info(msg)
        except Exception as e:
            # 如果日志记录失败，至少输出到控制台
            print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 日志记录异常: {e}")
            print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 原始消息: {msg}")

# 注册退出时的清理函数
def cleanup_logging():
    """程序退出时清理日志资源"""
    try:
        for handler in logger.handlers[:]:
            handler.close()
            logger.removeHandler(handler)
    except:
        pass

atexit.register(cleanup_logging)

# 全局异常处理器
def handle_exception(exc_type, exc_value, exc_traceback):
    """处理未捕获的异常"""
    if issubclass(exc_type, KeyboardInterrupt):
        # 允许Ctrl+C正常退出
        sys.__excepthook__(exc_type, exc_value, exc_traceback)
        return

    error_msg = f"未捕获的异常: {exc_type.__name__}: {exc_value}"
    try:
        log(error_msg)
        log(f"异常详情: {''.join(traceback.format_exception(exc_type, exc_value, exc_traceback))}")
    except:
        # 如果日志记录失败，至少输出到控制台
        print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] {error_msg}")
        traceback.print_exception(exc_type, exc_value, exc_traceback)

# 设置全局异常处理器
sys.excepthook = handle_exception

# ========== 状态映射 ==========
status_map = {
    "awaiting_packaging": "待打包",
    "awaiting_deliver": "待发货", 
    "awaiting_approve": "待确认",
    "awaiting_registration": "待入库",
    "acceptance_in_progress": "验货中",
    "arbitration": "纠纷中",
    "client_arbitration": "客户纠纷",
    "delivering": "配送中",
    "driver_pickup": "司机揽件中",
    "not_accepted": "未接单",
    "cancelled": "已取消",
    "sent_by_seller": "已发货",
    "delivered": "已送达",
    "returned": "已退货"
}

# ========== 配送方式映射 ==========
delivery_method_map = {
    "Ozon Логистика": "Ozon物流",
    "Самовывоз": "自提",
    "Курьерская доставка": "快递配送"
}
# ====================================

# ========== 主程序开始 ==========
def main():
    """主程序函数"""
    try:
        log("🚀 程序启动")

        cutoff_to = datetime.utcnow()
        cutoff_from = cutoff_to - timedelta(days=360)
        cutoff_to_str = cutoff_to.strftime("%Y-%m-%dT%H:%M:%S.000Z")
        cutoff_from_str = cutoff_from.strftime("%Y-%m-%dT%H:%M:%S.000Z")

        log(f"📅 查询时间范围: {cutoff_from_str} 到 {cutoff_to_str}")

        # 读取配置文件
        log(f"📖 读取配置文件: {input_file}")
        df = pd.read_excel(input_file)
        df = df.dropna(subset=['ID', '授权码'])
        log(f"✅ 成功读取 {len(df)} 个环境配置")

        all_orders = []
        global image_cache
        image_cache = {}  # 用于缓存已下载的图片
        start_time = time.time()

        return df, all_orders, start_time, cutoff_from_str, cutoff_to_str

    except Exception as e:
        log(f"❌ 程序初始化失败: {e}")
        raise

# 主程序执行函数
def run_main_program():
    """执行完整的主程序逻辑"""
    try:
        # 执行主程序初始化
        df, all_orders, start_time, cutoff_from_str, cutoff_to_str = main()
        return df, all_orders, start_time, cutoff_from_str, cutoff_to_str
    except Exception as e:
        log(f"❌ 主程序初始化失败: {e}")
        raise

# 下载并处理图片
def download_image(url, max_width=IMAGE_WIDTH, max_height=IMAGE_HEIGHT, max_retries=2):
    if not url:
        return None

    # 检查缓存
    if url in image_cache:
        return image_cache[url]

    for attempt in range(max_retries + 1):
        try:
            # 设置请求头，模拟浏览器
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }

            response = requests.get(
                url,
                headers=headers,
                timeout=(5, 15),  # (连接超时, 读取超时)
                proxies={"http": "http://127.0.0.1:7890", "https": "http://127.0.0.1:7890"} if use_proxy else None,
                stream=True  # 流式下载，避免大文件占用过多内存
            )

            if response.status_code == 200:
                # 检查内容长度，避免下载过大的文件
                content_length = response.headers.get('content-length')
                if content_length and int(content_length) > 5 * 1024 * 1024:  # 5MB限制
                    log(f"⚠️ 图片文件过大 ({content_length} bytes): {url}")
                    return None

                # 读取图片内容
                img_content = response.content

                # 使用PIL处理图片
                img = Image.open(io.BytesIO(img_content))

                # 转换为RGB模式（如果需要）
                if img.mode in ('RGBA', 'LA', 'P'):
                    img = img.convert('RGB')

                # 调整图片大小，保持比例
                width, height = img.size
                if width > max_width or height > max_height:
                    ratio = min(max_width/width, max_height/height)
                    new_width = int(width * ratio)
                    new_height = int(height * ratio)
                    img = img.resize((new_width, new_height), Image.LANCZOS)

                # 转换为字节流
                img_byte_arr = io.BytesIO()
                img_format = 'JPEG'  # 统一使用JPEG格式
                img.save(img_byte_arr, format=img_format, quality=85, optimize=True)
                img_byte_arr.seek(0)

                # 保存到缓存
                image_cache[url] = img_byte_arr
                return img_byte_arr
            else:
                log(f"⚠️ 下载图片失败 (状态码: {response.status_code}): {url}")
                if attempt < max_retries:
                    time.sleep(1)  # 重试前等待1秒
                    continue
                return None

        except requests.exceptions.Timeout as e:
            log(f"⚠️ 图片下载超时 (尝试 {attempt + 1}/{max_retries + 1}): {url}")
            if attempt < max_retries:
                time.sleep(2)  # 超时后等待更长时间
                continue
        except requests.exceptions.RequestException as e:
            log(f"⚠️ 网络请求异常 (尝试 {attempt + 1}/{max_retries + 1}): {e}")
            if attempt < max_retries:
                time.sleep(1)
                continue
        except Exception as e:
            log(f"⚠️ 处理图片异常 (尝试 {attempt + 1}/{max_retries + 1}): {e}")
            if attempt < max_retries:
                time.sleep(1)
                continue

    return None

# 获取商品图片信息 - 使用正确的API端点
def get_product_images(client_id, api_key, product_id, offer_id=None, posting_number=None):
    headers = {
        "Client-Id": client_id,
        "Api-Key": api_key,
        "Content-Type": "application/json"
    }
    
    # 方法1: 优先使用 offer_id
    if offer_id:
        try:
            log(f"🔍 尝试通过offer_id获取图片: {offer_id}")
            request_data = {
                "offer_id": [offer_id],
                "product_id": [],
                "sku": []
            }
            
            response = requests.post(
                "https://api-seller.ozon.ru/v3/product/info/list",
                headers=headers,
                json=request_data,
                proxies={"http": "http://127.0.0.1:7890", "https": "http://127.0.0.1:7890"} if use_proxy else None,
                timeout=10
            )
            
            log(f"📡 offer_id查询响应状态: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                items = result.get("items", [])
                
                if items:
                    item = items[0]
                    
                    # 优先使用 primary_image
                    primary_image = item.get("primary_image", "")
                    if primary_image:
                        # 如果是列表，取第一个元素；如果是字符串，直接使用
                        image_url = primary_image[0] if isinstance(primary_image, list) else primary_image
                        log(f"✅ offer_id找到primary_image: {image_url}")
                        return image_url
                    
                    # 其次使用 images 数组的第一张图片
                    images = item.get("images", [])
                    if images and len(images) > 0:
                        image_url = images[0] if isinstance(images[0], str) else images[0]
                        log(f"✅ offer_id找到images: {image_url}")
                        return image_url
                    
                    log(f"⚠️ offer_id查询到商品但无图片")
                else:
                    log(f"⚠️ offer_id未找到商品信息")
            else:
                log(f"❌ offer_id查询失败: {response.text}")
                
        except Exception as e:
            log(f"⚠️ offer_id查询异常: {e}")
    
    # 方法2: 使用 sku (product_id)
    if product_id and str(product_id).isdigit():
        try:
            log(f"🔍 尝试通过sku获取图片: {product_id}")
            request_data = {
                "offer_id": [],
                "product_id": [],
                "sku": [str(product_id)]
            }
            
            response = requests.post(
                "https://api-seller.ozon.ru/v3/product/info/list",
                headers=headers,
                json=request_data,
                proxies={"http": "http://127.0.0.1:7890", "https": "http://127.0.0.1:7890"} if use_proxy else None,
                timeout=10
            )
            
            log(f"📡 sku查询响应状态: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                items = result.get("items", [])
                
                if items:
                    item = items[0]
                    
                    # 优先使用 primary_image
                    primary_image = item.get("primary_image", "")
                    if primary_image:
                        # 如果是列表，取第一个元素；如果是字符串，直接使用
                        image_url = primary_image[0] if isinstance(primary_image, list) else primary_image
                        log(f"✅ sku找到primary_image: {image_url}")
                        return image_url
                    
                    # 其次使用 images 数组的第一张图片
                    images = item.get("images", [])
                    if images and len(images) > 0:
                        image_url = images[0] if isinstance(images[0], str) else images[0]
                        log(f"✅ sku找到images: {image_url}")
                        return image_url
                    
                    log(f"⚠️ sku查询到商品但无图片")
                else:
                    log(f"⚠️ sku未找到商品信息")
            else:
                log(f"❌ sku查询失败: {response.text}")
                
        except Exception as e:
            log(f"⚠️ sku查询异常: {e}")
    
    # 方法3: 使用 product_id
    if product_id and str(product_id).isdigit():
        try:
            log(f"🔍 尝试通过product_id获取图片: {product_id}")
            request_data = {
                "offer_id": [],
                "product_id": [str(product_id)],
                "sku": []
            }
            
            response = requests.post(
                "https://api-seller.ozon.ru/v3/product/info/list",
                headers=headers,
                json=request_data,
                proxies={"http": "http://127.0.0.1:7890", "https": "http://127.0.0.1:7890"} if use_proxy else None,
                timeout=10
            )
            
            log(f"📡 product_id查询响应状态: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                items = result.get("items", [])
                
                if items:
                    item = items[0]
                    
                    # 优先使用 primary_image
                    primary_image = item.get("primary_image", "")
                    if primary_image:
                        # 如果是列表，取第一个元素；如果是字符串，直接使用
                        image_url = primary_image[0] if isinstance(primary_image, list) else primary_image
                        log(f"✅ product_id找到primary_image: {image_url}")
                        return image_url
                    
                    # 其次使用 images 数组的第一张图片
                    images = item.get("images", [])
                    if images and len(images) > 0:
                        image_url = images[0] if isinstance(images[0], str) else images[0]
                        log(f"✅ product_id找到images: {image_url}")
                        return image_url
                    
                    log(f"⚠️ product_id查询到商品但无图片")
                else:
                    log(f"⚠️ product_id未找到商品信息")
            else:
                log(f"❌ product_id查询失败: {response.text}")
                
        except Exception as e:
            log(f"⚠️ product_id查询异常: {e}")
    
    log(f"❌ 所有方法都未能获取到图片")
    return ""

def process_env(row, cutoff_from_str, cutoff_to_str):
    environment_name = row['环境名']
    client_id = str(row['ID']).replace('.0', '')
    api_key = row['授权码']
    headers = {
        "Client-Id": client_id,
        "Api-Key": api_key,
        "Content-Type": "application/json"
    }

    local_orders = []
    offset = 0
    limit = 1000
    has_more = True

    log(f"\n🌍 开始处理环境: {environment_name}")

    while has_more:
        payload = {
            "dir": "ASC",
            "filter": {
                "cutoff_from": cutoff_from_str,
                "cutoff_to": cutoff_to_str
            },
            "limit": limit,
            "offset": offset,
            "with": {
                "analytics_data": True,
                "barcodes": True,
                "financial_data": True,
                "translit": True
            }
        }

        for attempt in range(retries):
            try:
                log(f"📦 请求待处理订单 offset={offset}")
                response = requests.post(
                    "https://api-seller.ozon.ru/v3/posting/fbs/unfulfilled/list",
                    headers=headers,
                    json=payload,
                    proxies={"http": "http://127.0.0.1:7890", "https": "http://127.0.0.1:7890"} if use_proxy else None,
                    timeout=20
                )

                log(f"⬅️ Status Code: {response.status_code}")

                if response.status_code == 200:
                    res_json = response.json()
                    result = res_json.get("result", {})
                    postings = result.get("postings", [])

                    if not postings:
                        has_more = False
                        break

                    for posting in postings:
                        status_en = posting.get("status")
                        status_cn = status_map.get(status_en, status_en)
                        
                        delivery_method = posting.get("delivery_method", {})
                        delivery_name = delivery_method.get("name", "")
                        delivery_name_cn = delivery_method_map.get(delivery_name, delivery_name)
                        
                        # 获取客户信息
                        customer = posting.get("customer", {})
                        
                        # 获取地址信息
                        addressee = posting.get("addressee", {})
                        
                        # 获取财务数据
                        financial_data = posting.get("financial_data", {})
                        
                        # 获取分析数据
                        analytics_data = posting.get("analytics_data", {})
                        
                        # 获取货运单号
                        tracking_number = posting.get("tracking_number", "")

                        base = {
                            "环境名": environment_name,
                            "订单编号": posting.get("posting_number"),
                            "货运单号": tracking_number,
                            "order_number": posting.get("order_number"),
                            "order_id": posting.get("order_id"),
                            "status": status_en,
                            "状态中文": status_cn,
                            "created_at": posting.get("created_at"),
                            "in_process_at": posting.get("in_process_at"),
                            "shipment_date": posting.get("shipment_date"),
                            "delivering_date": posting.get("delivering_date"),
                            "delivery_method_id": delivery_method.get("id"),
                            "delivery_method": delivery_name,
                            "delivery_method_cn": delivery_name_cn,
                            "warehouse_id": delivery_method.get("warehouse_id"),
                            "warehouse": delivery_method.get("warehouse"),
                            "tpl_provider_id": delivery_method.get("tpl_provider_id"),
                            "tpl_provider": delivery_method.get("tpl_provider"),
                            "customer_id": customer.get("customer_id"),
                            "customer_name": customer.get("name"),
                            "addressee_name": addressee.get("name"),
                            "addressee_phone": addressee.get("phone"),
                            "city": addressee.get("city"),
                            "region": addressee.get("region"),
                            "country": addressee.get("country"),
                            "zip_code": addressee.get("zip_code"),
                            "address": addressee.get("address"),
                            "comment": addressee.get("comment"),
                            "total_discount_value": financial_data.get("total_discount_value"),
                            "total_discount_percent": financial_data.get("total_discount_percent"),
                            "cluster_from": analytics_data.get("cluster_from"),
                            "cluster_to": analytics_data.get("cluster_to"),
                            "delivery_type": analytics_data.get("delivery_type"),
                            "is_premium": analytics_data.get("is_premium"),
                            "payment_type_group_name": analytics_data.get("payment_type_group_name"),
                            "warehouse_name": analytics_data.get("warehouse_name"),
                            "warehouse_type": analytics_data.get("warehouse_type"),
                            "is_legal": analytics_data.get("is_legal")
                        }

                        for product in posting.get("products", []):
                            data = base.copy()
                            product_id = product.get("sku")
                            offer_id = product.get("offer_id")
                            
                            data["平台SKU"] = product_id
                            data["offer_id"] = offer_id
                            data["name"] = product.get("name")
                            data["商品数量"] = product.get("quantity")
                            data["商品价格"] = product.get("price")
                            data["mandatory_mark"] = product.get("mandatory_mark")
                            data["currency_code"] = product.get("currency_code")
                            
                            # 获取商品图片链接 - 传递更多参数
                            image_url = get_product_images(client_id, api_key, product_id, offer_id, posting.get("posting_number"))
                            data["SKU图片链接"] = image_url
                            
                            # 财务数据
                            financial_data_product = product.get("financial_data", {})
                            data["product_id"] = financial_data_product.get("product_id")
                            data["commission_amount"] = financial_data_product.get("commission_amount")
                            data["commission_percent"] = financial_data_product.get("commission_percent")
                            data["payout"] = financial_data_product.get("payout")
                            data["product_discount_percent"] = financial_data_product.get("product_discount_percent")
                            data["product_discount_value"] = financial_data_product.get("product_discount_value")
                            data["old_price"] = financial_data_product.get("old_price")
                            data["total_discount_value_product"] = financial_data_product.get("total_discount_value")
                            data["total_discount_percent_product"] = financial_data_product.get("total_discount_percent")
                            data["actions"] = str(financial_data_product.get("actions", []))
                            
                            # 条形码信息
                            barcodes = product.get("barcodes", [])
                            data["barcodes"] = ", ".join(barcodes) if barcodes else ""

                            log(f"✅ 订单 {data['订单编号']} 商品 {data['offer_id']} 数量 {data['商品数量']} 价格 {data['商品价格']} 图片 {bool(data['SKU图片链接'])}")
                            local_orders.append(data)

                    offset += limit
                    has_more = result.get("has_next", False)
                    time.sleep(random.uniform(0.3, 1.2))
                    break
                else:
                    log(f"❌ 非200响应: {response.text}")
                    has_more = False
                    break
            except requests.exceptions.RequestException as e:
                log(f"⚠️ 请求异常: {e}，重试 {attempt + 1}/{retries}")
                time.sleep(1)

    return local_orders

def process_orders_multithread(df, all_orders, cutoff_from_str, cutoff_to_str):
    """多线程处理订单数据"""
    try:
        log(f"🔄 开始多线程处理，最大工作线程数: {MAX_WORKERS}")
        with ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
            futures = [executor.submit(process_env, row, cutoff_from_str, cutoff_to_str) for _, row in df.iterrows()]
            completed_count = 0
            total_count = len(futures)

            for future in as_completed(futures):
                try:
                    result = future.result()
                    all_orders.extend(result)
                    completed_count += 1
                    log(f"📊 环境处理进度: {completed_count}/{total_count}")
                except Exception as e:
                    log(f"❌ 处理环境时出现异常: {e}")
                    completed_count += 1

        log(f"✅ 多线程处理完成，共获取 {len(all_orders)} 条订单数据")
        return True

    except Exception as e:
        log(f"❌ 多线程处理失败: {e}")
        return False

def create_output_dataframe(all_orders):
    """创建并优化输出数据表"""
    try:
        log("📊 开始创建输出数据表...")
        df_output = pd.DataFrame(all_orders)
        log(f"✅ 成功创建数据表，包含 {len(df_output)} 行数据")

        if not df_output.empty:
            # 添加SKU图片列
            if "SKU图片" not in df_output.columns:
                df_output.insert(df_output.columns.get_loc("SKU图片链接"), "SKU图片", "")

            # 重新排列列顺序，将你特别需要的字段放在最前面
            column_order = [
                "环境名", "订单编号", "货运单号", "平台SKU", "SKU图片", "SKU图片链接", "商品数量", "商品价格",
                "order_number", "order_id", "status", "状态中文", "created_at", "in_process_at",
                "shipment_date", "delivering_date", "offer_id", "name", "currency_code",
                "commission_amount", "commission_percent", "payout",
                "product_discount_value", "product_discount_percent",
                "old_price", "total_discount_value_product", "total_discount_percent_product",
                "customer_name", "addressee_name", "addressee_phone",
                "city", "region", "country", "address", "zip_code",
                "delivery_method", "delivery_method_cn", "warehouse",
                "payment_type_group_name", "is_premium", "is_legal",
                "barcodes", "mandatory_mark", "actions", "comment"
            ]

            # 只保留存在的列
            existing_columns = [col for col in column_order if col in df_output.columns]
            remaining_columns = [col for col in df_output.columns if col not in existing_columns]
            final_columns = existing_columns + remaining_columns

            df_output = df_output[final_columns]

        return df_output

    except Exception as e:
        log(f"❌ 创建数据表失败: {e}")
        raise

def save_excel_with_images(df_output, order_output_file):
    """保存Excel文件并插入图片"""
    try:
        log(f"💾 开始保存Excel文件: {order_output_file}")

        # 检查数据是否为空
        if df_output.empty:
            log("⚠️ 数据为空，保存空的Excel文件")
            df_output.to_excel(order_output_file, index=False)
            log(f"✅ 空Excel文件保存成功: {order_output_file}")
            return True

        with pd.ExcelWriter(order_output_file, engine='openpyxl') as writer:
            df_output.to_excel(writer, index=False, sheet_name='订单数据')
            worksheet = writer.sheets['订单数据']

            # 设置行高以适应图片
            for row_idx in range(2, len(df_output) + 2):  # +2 因为Excel行从1开始，且有标题行
                worksheet.row_dimensions[row_idx].height = 60

            # 设置列宽
            for idx, col in enumerate(df_output.columns):
                if idx < 702:  # Excel最多支持702列 (A到ZZZ)
                    try:
                        # 使用更简单的方法获取列字母
                        col_letter = get_column_letter(idx + 1)

                        # 为图片列设置更宽的宽度
                        if col == "SKU图片":
                            worksheet.column_dimensions[col_letter].width = 15
                        else:
                            max_len = max(df_output[col].astype(str).map(len).max(), len(str(col))) + 2
                            worksheet.column_dimensions[col_letter].width = min(max_len, 50)
                    except Exception as e:
                        log(f"⚠️ 设置列宽异常 {idx}: {e}")
                        continue

            # 检查是否有图片相关列
            if "SKU图片" not in df_output.columns or "SKU图片链接" not in df_output.columns:
                log("⚠️ 没有图片相关列，跳过图片处理")
                log(f"✅ Excel文件保存成功: {order_output_file}")
                return True

            # 获取图片列和URL列的索引
            img_col_idx = df_output.columns.get_loc("SKU图片") + 1  # +1 因为Excel列从1开始
            url_col_idx = df_output.columns.get_loc("SKU图片链接") + 1

            # 下载并插入图片
            log("开始下载并插入图片...")
            total_images = sum(1 for url in df_output["SKU图片链接"] if url)
            processed_images = 0
            successful_images = 0

            for row_idx, url in enumerate(df_output["SKU图片链接"], start=2):  # 从第2行开始（跳过标题行）
                if url:
                    processed_images += 1
                    try:
                        log(f"📥 正在处理图片 {processed_images}/{total_images}: 行{row_idx}")
                        img_data = download_image(url)
                        if img_data:
                            try:
                                cell_address = f"{get_column_letter(img_col_idx)}{row_idx}"
                                img = XLImage(img_data)
                                worksheet.add_image(img, cell_address)
                                successful_images += 1
                                log(f"✅ 成功插入图片到单元格 {cell_address} ({successful_images}/{total_images})")
                            except Exception as img_insert_error:
                                # 图片插入Excel时的异常
                                error_msg = f"⚠️ Excel图片插入异常 行{row_idx}: {img_insert_error}"
                                log(error_msg)
                        else:
                            log(f"⚠️ 图片下载失败 行{row_idx}: {url}")
                    except Exception as e:
                        # 整个图片处理过程的异常
                        error_msg = f"⚠️ 图片处理异常 行{row_idx}: {str(e)}"
                        log(error_msg)

                    # 每处理10张图片显示一次进度
                    if processed_images % 10 == 0:
                        log(f"📊 图片处理进度: {processed_images}/{total_images} (成功: {successful_images})")

            log(f"🖼️ 图片处理完成: 总计{total_images}张，成功{successful_images}张，失败{total_images - successful_images}张")

        log(f"✅ Excel文件保存成功: {order_output_file}")
        return True

    except Exception as e:
        log(f"❌ Excel文件保存失败: {e}")
        # 尝试保存一个简化版本（不包含图片）
        try:
            simple_output_file = order_output_file.replace('.xlsx', '_简化版.xlsx')
            log(f"🔄 尝试保存简化版Excel文件: {simple_output_file}")
            df_output.to_excel(simple_output_file, index=False)
            log(f"✅ 简化版Excel文件保存成功: {simple_output_file}")
            return True
        except Exception as simple_save_error:
            log(f"❌ 简化版Excel文件保存也失败: {simple_save_error}")
            return False

def cleanup_and_finish(start_time, df, all_orders, image_cache, order_output_file):
    """清理资源并完成程序"""
    end_time = time.time()
    elapsed = end_time - start_time
    d, rem = divmod(elapsed, 86400)
    h, rem = divmod(rem, 3600)
    m, s = divmod(rem, 60)

    # 程序完成统计
    log(f"\n✅ 完成！已保存待处理订单表格：{order_output_file}")
    log(f"🌍 总环境数：{len(df)}")
    log(f"📦 总订单数：{len(all_orders)}")
    log(f"🖼️ 图片缓存数：{len(image_cache)}")
    log(f"⏱️ 总耗时：{int(d)}天 {int(h)}小时 {int(m)}分钟 {int(s)}秒")

    # 清理资源
    try:
        # 清理图片缓存
        for url, img_data in image_cache.items():
            try:
                if hasattr(img_data, 'close'):
                    img_data.close()
            except:
                pass
        image_cache.clear()
        log("🧹 已清理图片缓存")
    except Exception as e:
        log(f"⚠️ 清理缓存时出现异常: {e}")

    # 最终日志记录
    log("🎉 程序执行完毕！")

    # 确保日志被写入文件
    try:
        for handler in logger.handlers:
            if hasattr(handler, 'flush'):
                handler.flush()
    except:
        pass


def run_complete_program():
    """运行完整的Ozon订单获取程序"""
    try:
        # 1. 初始化程序
        df, all_orders, start_time, cutoff_from_str, cutoff_to_str = run_main_program()

        # 2. 多线程处理订单
        if not process_orders_multithread(df, all_orders, cutoff_from_str, cutoff_to_str):
            log("❌ 多线程处理失败，程序终止")
            return False

        # 3. 创建输出数据表
        df_output = create_output_dataframe(all_orders)

        # 4. 保存Excel文件
        if not save_excel_with_images(df_output, order_output_file):
            log("❌ Excel文件保存失败，程序终止")
            return False

        # 5. 清理和完成
        cleanup_and_finish(start_time, df, all_orders, image_cache, order_output_file)

        return True

    except Exception as e:
        log(f"❌ 程序执行过程中出现严重错误: {e}")
        log(f"异常详情: {traceback.format_exc()}")
        return False


# 程序入口点
if __name__ == "__main__":
    try:
        success = run_complete_program()
        if not success:
            log("❌ 程序执行失败")
            sys.exit(1)
    except KeyboardInterrupt:
        log("⚠️ 程序被用户中断")
        sys.exit(0)
    except Exception as e:
        log(f"❌ 程序发生未预期的错误: {e}")
        sys.exit(1)
