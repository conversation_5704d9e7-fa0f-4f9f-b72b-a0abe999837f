import os
os.environ.pop("HTTP_PROXY", None)
os.environ.pop("HTTPS_PROXY", None)
os.environ["NO_PROXY"] = "*"

import pandas as pd
import requests
import time
import random
import logging
import io
import sys
import schedule
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
import queue
import threading

# ========== 全局配置 ==========
input_file = r'D:\gx\查询状态上库存归档报活动店铺表格\查询商品状态数量统计-上库存-归档-20250426.xlsx'
log_dir    = r'D:\gx'
use_proxy  = False
retries    = 3
MAX_WORKERS= 20
# 时间范围：2025-01-01 到 当前
date_from  = "2025-04-01T00:00:00Z"
limit      = 500
# ============================

# ========== 可选代理 ==========
proxies = {"http":"http://127.0.0.1:7890","https":"http://127.0.0.1:7890"} if use_proxy else None

def setup_logger(ts: str):
    """根据时间戳初始化日志记录器并返回 logger"""
    log_file = os.path.join(log_dir, f'退货申请日志_{ts}.log')
    sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
    logger = logging.getLogger()
    logger.setLevel(logging.INFO)
    logger.handlers.clear()

    fh = logging.FileHandler(log_file, encoding='utf-8-sig')
    fh.setFormatter(logging.Formatter('[%(asctime)s] %(message)s'))
    ch = logging.StreamHandler(sys.stdout)
    ch.setFormatter(logging.Formatter('[%(asctime)s] %(message)s'))

    logger.addHandler(fh)
    logger.addHandler(ch)
    return logger

def run_all_returns():
    ts = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = os.path.join(log_dir, f'退货申请明细_{ts}.xlsx')
    logger = setup_logger(ts)

    def log(msg):
        logger.info(msg)

    # 线程安全队列
    result_queue = queue.Queue()

    # 写入线程
    def writer_thread():
        buf = []
        flush_interval = 1
        last_flush = time.time()
        while True:
            try:
                rec = result_queue.get(timeout=1)
                if rec is None:
                    break
                buf.extend(rec if isinstance(rec, list) else [rec])
                now = time.time()
                if now - last_flush >= flush_interval and buf:
                    df_new = pd.DataFrame(buf)
                    if os.path.exists(output_file):
                        df_old = pd.read_excel(output_file)
                    else:
                        df_old = pd.DataFrame()
                    pd.concat([df_old, df_new], ignore_index=True)\
                      .to_excel(output_file, index=False)
                    log(f"💾 已批量保存 {len(buf)} 条到：{output_file}")
                    buf.clear()
                    last_flush = now
                result_queue.task_done()
            except queue.Empty:
                now = time.time()
                if buf and now - last_flush >= flush_interval:
                    df_new = pd.DataFrame(buf)
                    if os.path.exists(output_file):
                        df_old = pd.read_excel(output_file)
                    else:
                        df_old = pd.DataFrame()
                    pd.concat([df_old, df_new], ignore_index=True)\
                      .to_excel(output_file, index=False)
                    log(f"💾 已批量保存 {len(buf)} 条到：{output_file}")
                    buf.clear()
                    last_flush = now

    # 单环境处理
    def process_env(row):
        env = row['环境名']
        client_id = str(row['ID']).replace('.0','')
        api_key    = row['授权码']
        headers = {"Client-Id": client_id, "Api-Key": api_key}

        log(f"\n🌍 开始环境：{env}")
        last_id = 0
        return_ids = []

        # 拉取 ID 列表
        while True:
            body = {
                "filter": {"created_at": {"from": date_from, "to": datetime.utcnow().strftime("%Y-%m-%dT23:59:59Z")}},
                "limit":   limit,
                "last_id": last_id
            }
            try:
                resp = requests.post(
                    'https://api-seller.ozon.ru/v2/returns/rfbs/list',
                    headers=headers,
                    json=body,
                    proxies=proxies,
                    timeout=20
                )
                log(f"⬅️ List {resp.status_code}，{env}")
                if resp.status_code != 200:
                    log(f"❌ List失败：{resp.status_code}，{env}")
                    break
                data = resp.json().get("returns", [])
                if not data:
                    break
                return_ids.extend(data)
                last_id = max(item.get("return_id",0) for item in data)
                if len(data) < limit:
                    break
            except Exception as e:
                log(f"❗ List异常: {e}，{env}")
                break
            time.sleep(random.uniform(0.1,0.3))

        log(f"🔢 环境 {env} 收集 {len(return_ids)} 条ID")

        details = []
        # 拉取每条详情
        for item in return_ids:
            rid = item.get("return_id")
            for _ in range(retries):
                try:
                    resp = requests.post(
                        'https://api-seller.ozon.ru/v2/returns/rfbs/get',
                        headers=headers,
                        json={"return_id": rid},
                        proxies=proxies,
                        timeout=15
                    )
                    if resp.status_code == 200:
                        info = resp.json().get("returns",{})
                        details.append({
                            "环境名": env,
                            "退货申请ID": info.get("return_id"),
                            "退货申请编号": info.get("return_number"),
                            "状态": info.get("state",{}).get("state"),
                            "状态名称": info.get("state",{}).get("state_name"),
                            "申请时间": info.get("created_at"),
                            "货件编号": info.get("posting_number")
                        })
                        break
                    else:
                        log(f"❌ Get失败 {resp.status_code}，{env} ID={rid}")
                except Exception as e:
                    log(f"❗ Get异常: {e}，{env} ID={rid}")
            time.sleep(random.uniform(0.05,0.2))

        result_queue.put(details)
        log(f"✅ 环境 {env} 推送 {len(details)} 条")
        return env

    # 读取环境
    df = pd.read_excel(input_file).dropna(subset=['ID','授权码'])
    total = len(df)
    start = time.time()

    writer = threading.Thread(target=writer_thread, daemon=True)
    writer.start()

    with ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
        futures = [executor.submit(process_env, row) for _, row in df.iterrows()]
        for f in as_completed(futures):
            try:
                e = f.result()
                log(f"🏁 完成环境: {e}")
            except Exception as e:
                log(f"❌ 异常环境: {e}")

    result_queue.put(None)
    writer.join()

    # ✅ 修复日志错误：防止线程继续写日志到关闭的文件
    for handler in logger.handlers:
        handler.close()
        logger.removeHandler(handler)

    elapsed = time.time() - start
    d, rem = divmod(elapsed,86400)
    h, rem = divmod(rem,3600)
    m, s = divmod(rem,60)
    log(f"\n🎉 全部完成！共 {total} 环境，结果: {output_file}")
    log(f"⏱️ 耗时：{int(d)}天{int(h)}时{int(m)}分{int(s)}秒")

# ========== 定时调度 ==========
if __name__ == "__main__":
    # 安装依赖：pip install schedule
    schedule.every().day.at("12:20").do(run_all_returns)
    # schedule.every().day.at("12:00").do(run_all_returns)
    # schedule.every().day.at("16:00").do(run_all_returns)
    schedule.every().day.at("20:00").do(run_all_returns)

    print("📅 定时任务已启动，等待固定时间执行…")
    while True:
        schedule.run_pending()
        time.sleep(5)
