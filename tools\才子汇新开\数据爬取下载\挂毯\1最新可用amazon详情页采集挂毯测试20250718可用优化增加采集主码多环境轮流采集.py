# -*- coding: utf-8 -*-
import os
import sys
import time
import random
import pandas as pd
import subprocess
import json
from datetime import datetime
from threading import Thread, Lock
from playwright.sync_api import sync_playwright, TimeoutError as PlaywrightTimeout

sys.stdout.reconfigure(encoding='utf-8')

# === 配置区 ===
DETAIL_INPUT_EXCEL = r"D:\待处理\采集挂毯amazon_scraped_results_20250719_095711.xlsx"
ENV_EXCEL = r"D:\hubtest\pacong-environment2.xlsx"
TIMESTAMP = datetime.now().strftime('%Y%m%d_%H%M%S')
OUTPUT_EXCEL = f"采集挂毯详情页amazon_detail_sku_results_{TIMESTAMP}.xlsx"

SAVE_INTERVAL_SECONDS = 60
save_lock = Lock()
all_sku_data = []


def save_to_excel_realtime():
    with save_lock:
        if not all_sku_data:
            return
        try:
            df_new = pd.DataFrame(all_sku_data)
            if os.path.exists(OUTPUT_EXCEL):
                df_existing = pd.read_excel(OUTPUT_EXCEL)
                df_combined = pd.concat([df_existing, df_new], ignore_index=True)
            else:
                df_combined = df_new
            df_combined.to_excel(OUTPUT_EXCEL, index=False)
            print(f"💾 已保存 {len(df_new)} 条 SKU 记录，总计 {len(df_combined)} 条")
            all_sku_data.clear()
        except Exception as e:
            print(f"❌ 保存详情页 Excel 失败: {e}")


def periodic_save():
    while True:
        time.sleep(SAVE_INTERVAL_SECONDS)
        save_to_excel_realtime()


def start_container_and_get_port(container_code):
    url = 'http://127.0.0.1:6873/api/v1/browser/start'
    cmd = [
        'curl', '-s', '-X', 'POST', url,
        '-H', 'Content-Type: application/json',
        '-d', json.dumps({"containerCode": container_code})
    ]
    res = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    try:
        data = json.loads(res.stdout.decode('utf-8', 'ignore'))
        if data.get('code') == 0:
            port = int(data['data']['debuggingPort'])
            print(f"[{container_code}] 🔌 获取调试端口: {port}")
            return port
    except:
        pass
    print(f"❌ 容器 {container_code} 启动或获取端口失败: {res.stdout.decode('utf-8', 'ignore')}")
    return None


def safe_inner_text(locator, timeout=5000):
    try:
        return locator.first.inner_text(timeout=timeout).strip()
    except:
        return ""


def extract_sku_details(page, base_record, env_name):
    results = []

    try:
        page.wait_for_selector('#productTitle', timeout=15000)
    except:
        print(f"[{env_name}] ⚠️ 页面中未找到商品标题 #productTitle")
        return []

    try:
        # 直接在twister容器里找有图片的SKU选项
        sku_options = page.locator('#twister-plus-inline-twister-card li:has(img)')
        
        if sku_options.count() > 0:
            count = sku_options.count()
            print(f"[{env_name}] 🎨 找到 {count} 个有图片的SKU选项")

            for idx in range(count):
                time.sleep(random.uniform(2.0, 3.0))
                opt = sku_options.nth(idx)
                try:
                    # 点击SKU选项
                    if opt.locator('button').count():
                        opt.locator('button').first.click()
                    else:
                        opt.click()

                    time.sleep(random.uniform(2.0, 3.0))

                    current_url = page.url
                    title = safe_inner_text(page.locator('#productTitle'))
                    
                    # 获取颜色信息
                    color = ""
                    if page.locator('#variation_color_name .selection').count():
                        color = safe_inner_text(page.locator('#variation_color_name .selection'))
                    elif page.locator('#inline-twister-expanded-dimension-text-color_name').count():
                        color = safe_inner_text(page.locator('#inline-twister-expanded-dimension-text-color_name'))
                    
                    # 获取主图
                    img_src = ""
                    big_img = ""
                    
                    if page.locator('li.image.item.maintain-height.selected').count():
                        sel_li = page.locator('li.image.item.maintain-height.selected').first
                        img_el = sel_li.locator('img').first
                        img_src = img_el.get_attribute('src') or ''
                        big_img = img_el.get_attribute('data-old-hires') or img_src
                    elif page.locator('#imgTagWrapperId img').count():
                        img_el = page.locator('#imgTagWrapperId img').first
                        img_src = img_el.get_attribute('src') or ''
                        big_img = img_el.get_attribute('data-old-hires') or img_src
                    elif page.locator('#landingImage').count():
                        img_el = page.locator('#landingImage').first
                        img_src = img_el.get_attribute('src') or ''
                        big_img = img_el.get_attribute('data-old-hires') or img_src

                    record = {
                        **base_record,
                        '环境': env_name,
                        '详情采集时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                        '当前链接': current_url,
                        'SKU 标题': title,
                        'SKU 颜色': color,
                        '主图链接': img_src,
                        '大图链接': big_img,
                        '选择器类型': 'twister_with_image'
                    }
                    results.append(record)
                    print(f"[{env_name}] ✅ 提取SKU: {color} | {title[:50]}...")
                except Exception as e:
                    print(f"[{env_name}] ⚠️ SKU 提取失败 (选项 {idx}): {e}")

        else:
            print(f"[{env_name}] ✅ 单SKU商品，提取默认主图")
            current_url = page.url
            title = safe_inner_text(page.locator('#productTitle'))
            
            color = ""
            if page.locator('#variation_color_name .selection').count():
                color = safe_inner_text(page.locator('#variation_color_name .selection'))

            img_src = ""
            big_img = ""
            
            if page.locator('li.image.item.maintain-height.selected').count():
                sel_li = page.locator('li.image.item.maintain-height.selected').first
                img_el = sel_li.locator('img').first
                img_src = img_el.get_attribute('src') or ''
                big_img = img_el.get_attribute('data-old-hires') or img_src
            elif page.locator('#imgTagWrapperId img').count():
                img_el = page.locator('#imgTagWrapperId img').first
                img_src = img_el.get_attribute('src') or ''
                big_img = img_el.get_attribute('data-old-hires') or img_src
            elif page.locator('#landingImage').count():
                img_el = page.locator('#landingImage').first
                img_src = img_el.get_attribute('src') or ''
                big_img = img_el.get_attribute('data-old-hires') or img_src

            record = {
                **base_record,
                '环境': env_name,
                '详情采集时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                '当前链接': current_url,
                'SKU 标题': title,
                'SKU 颜色': color,
                '主图链接': img_src,
                '大图链接': big_img,
                '选择器类型': 'single_sku'
            }
            results.append(record)

    except Exception as e:
        print(f"[{env_name}] ❌ 处理 SKU 区域失败: {e}")

    return results




def read_env_excel(path):
    df = pd.read_excel(path)
    return [
        (str(int(r['环境ID'])), str(r['环境名称']))
        for _, r in df.iterrows()
        if pd.notna(r['环境ID']) and pd.notna(r['环境名称'])
    ]


def read_detail_excel(path):
    df = pd.read_excel(path)
    records = []
    for _, row in df.iterrows():
        records.append({
            '采集时间': row.get('采集时间', ''),
            '搜索词': row.get('搜索词', ''),
            '列表标题': row.get('标题', ''),
            '价格': row.get('价格', ''),
            '评分': row.get('评分', ''),
            '评价数量': row.get('评价数量', ''),
            '商品链接': row.get('商品链接', '')
        })
    return records


def run_detail_on_page(env_code, env_name, page, record):
    try:
        print(f"[{env_name}] 🌐 打开详情页: {record['商品链接']}")
        page.goto(record['商品链接'], timeout=60000)
        page.wait_for_load_state('load', timeout=60000)
        sku_details = extract_sku_details(page, record, env_name)
        with save_lock:
            all_sku_data.extend(sku_details)
    except PlaywrightTimeout:
        print(f"[{env_name}] ❌ 详情页加载超时，重试一次")
        try:
            page.goto(record['商品链接'], timeout=120000)
            page.wait_for_load_state('load', timeout=60000)
            sku_details = extract_sku_details(page, record, env_name)
            with save_lock:
                all_sku_data.extend(sku_details)
        except Exception as e:
            print(f"[{env_name}] ❌ 详情页重试失败: {e}")
    except Exception as e:
        print(f"[{env_name}] ❌ 详情页采集失败: {e}")


def main():
    env_list = read_env_excel(ENV_EXCEL)
    playwright = sync_playwright().start()
    ctx_map = {}
    for code, name in env_list:
        browser = None
        port = None
        for attempt in range(5):
            port = start_container_and_get_port(code)
            if not port:
                print(f"[{name}] ❌ 获取端口失败，重试 {attempt+1}/5")
                time.sleep(3)
                continue
            # 等待 CDP 服务启动
            time.sleep(3)
            print(f"[{name}] 🔌 连接 CDP 地址: ws://127.0.0.1:{port}")
            try:
                browser = playwright.chromium.connect_over_cdp(
                    f"http://127.0.0.1:{port}", timeout=120000
                )
                print(f"[{name}] ✅ CDP 连接成功")
                break
            except PlaywrightTimeout:
                print(f"[{name}] ❌ CDP 连接超时 (port={port})，重试 {attempt+1}/5...")
        if not browser:
            print(f"[{name}] ❌ 无法连接 CDP，多次重试失败，跳过此环境")
            continue
        context = browser.contexts[0]
        page = context.pages[0] if context.pages else context.new_page()
        ctx_map[code] = {'browser': browser, 'page': page, 'name': name}

    if not ctx_map:
        print("❌ 没有可用的环境，退出程序。")
        return

    Thread(target=periodic_save, daemon=True).start()

    detail_list = read_detail_excel(DETAIL_INPUT_EXCEL)
    codes = list(ctx_map.keys())

    for idx, record in enumerate(detail_list):
        code = codes[idx % len(codes)]
        env_name = ctx_map[code]['name']
        page = ctx_map[code]['page']
        run_detail_on_page(code, env_name, page, record)

    save_to_excel_realtime()
    print(f"🎉 详情 SKU 采集完成，共 {len(all_sku_data)} 条记录！")


if __name__ == '__main__':
    main()
