import pandas as pd
import requests
import os

# 定义文件路径和输出目录
file_path = 'C:\\Users\\<USER>\\Downloads\\三件套订单250721-酷比.xlsx'
output_dir = 'C:\\Users\\<USER>\\Downloads\\三件套'

# 创建保存图片的目录
os.makedirs(output_dir, exist_ok=True)

# 读取第一个工作表
df = pd.read_excel(file_path, sheet_name=0)

# 遍历每一行
for index, row in df.iterrows():
    order_id = str(row.get('订单编号')).strip()
    image_url = str(row.get('SKU图片链接')).strip()

    if pd.isna(order_id) or pd.isna(image_url):
        print(f"第 {index+2} 行缺少必要信息，跳过")
        continue

    file_name = f"{order_id}.jpg"
    save_path = os.path.join(output_dir, file_name)

    try:
        response = requests.get(image_url, timeout=10)
        response.raise_for_status()

        with open(save_path, 'wb') as f:
            f.write(response.content)
        print(f"✅ 图片已下载: {file_name}")
    except requests.RequestException as e:
        print(f"❌ 下载失败（订单编号: {order_id}）: {e}")

print(f"\n全部下载完成，图片保存在目录: {output_dir}")
