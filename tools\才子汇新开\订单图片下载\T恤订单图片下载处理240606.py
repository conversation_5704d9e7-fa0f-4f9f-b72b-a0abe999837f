import pandas as pd
import requests
import os

# 定义文件路径和输出目录
file_path = 'C:\\Users\\<USER>\\Downloads\\宏信T恤订单尺码250721-采集模板.xlsx'
output_dir = 'C:\\Users\\<USER>\\Downloads\\T恤\\宏信250721'

# 创建保存图片的目录
os.makedirs(output_dir, exist_ok=True)

# 读取Excel文件中的第一个Sheet
df = pd.read_excel(file_path, sheet_name=0)

# 从第二行开始，遍历数据行
for index, row in df.iterrows():
    size = row['平台SKU']
    image_url = row['SKU图片链接']
    file_name = f"{index + 1}-{size}.jpg"
    file_path = os.path.join(output_dir, file_name)

    try:
        # 下载图片
        response = requests.get(image_url)
        response.raise_for_status()

        # 保存图片
        with open(file_path, 'wb') as file:
            file.write(response.content)
        print(f"✅ 图片已下载并保存为: {file_name}")
    except requests.RequestException as e:
        print(f"❌ 下载图片失败: {image_url}, 错误信息: {e}")

print(f"\n📁 所有图片已下载并保存在: {output_dir}")
