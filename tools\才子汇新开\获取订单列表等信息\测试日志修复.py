#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试日志修复脚本
用于验证修复后的日志系统是否正常工作
"""

import os
import sys
import time
import threading
import logging
from datetime import datetime

# 模拟原始的日志配置
timestamp = time.strftime("%Y%m%d_%H%M%S")
log_file = fr'D:\hubtest\测试日志修复_{timestamp}.log'

# ========== 修复后的日志配置 ==========
import atexit

# 创建线程锁确保日志操作的线程安全
log_lock = threading.Lock()

# 创建专用的logger实例，避免与其他模块冲突
logger = logging.getLogger('test_log_fix')
logger.setLevel(logging.INFO)

# 清除可能存在的旧handlers
logger.handlers.clear()

# 创建文件处理器
file_handler = logging.FileHandler(log_file, encoding='utf-8-sig')
file_handler.setFormatter(logging.Formatter('[%(asctime)s] %(message)s'))

# 创建控制台处理器
console_handler = logging.StreamHandler(sys.stdout)
console_handler.setLevel(logging.INFO)
console_handler.setFormatter(logging.Formatter('[%(asctime)s] %(message)s'))

# 添加处理器
logger.addHandler(file_handler)
logger.addHandler(console_handler)

# 防止日志传播到根logger
logger.propagate = False

def log(msg):
    """线程安全的日志记录函数"""
    with log_lock:
        try:
            # 检查文件处理器是否仍然有效
            if file_handler.stream and not file_handler.stream.closed:
                logger.info(msg)
            else:
                # 如果文件处理器已关闭，重新创建
                global file_handler
                logger.removeHandler(file_handler)
                file_handler = logging.FileHandler(log_file, encoding='utf-8-sig', mode='a')
                file_handler.setFormatter(logging.Formatter('[%(asctime)s] %(message)s'))
                logger.addHandler(file_handler)
                logger.info(msg)
        except Exception as e:
            # 如果日志记录失败，至少输出到控制台
            print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 日志记录异常: {e}")
            print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 原始消息: {msg}")

# 注册退出时的清理函数
def cleanup_logging():
    """程序退出时清理日志资源"""
    try:
        for handler in logger.handlers[:]:
            handler.close()
            logger.removeHandler(handler)
    except:
        pass

atexit.register(cleanup_logging)

def test_normal_logging():
    """测试正常日志记录"""
    log("🧪 开始测试正常日志记录")
    for i in range(5):
        log(f"📝 正常日志消息 {i+1}")
        time.sleep(0.1)
    log("✅ 正常日志记录测试完成")

def test_exception_logging():
    """测试异常情况下的日志记录"""
    log("🧪 开始测试异常日志记录")
    try:
        # 模拟一个异常
        raise ValueError("这是一个测试异常")
    except Exception as e:
        log(f"⚠️ 捕获到异常: {e}")
    log("✅ 异常日志记录测试完成")

def test_multithread_logging():
    """测试多线程日志记录"""
    log("🧪 开始测试多线程日志记录")
    
    def worker_thread(thread_id):
        for i in range(3):
            log(f"🔄 线程{thread_id} 消息{i+1}")
            time.sleep(0.05)
    
    threads = []
    for i in range(3):
        t = threading.Thread(target=worker_thread, args=(i+1,))
        threads.append(t)
        t.start()
    
    for t in threads:
        t.join()
    
    log("✅ 多线程日志记录测试完成")

def test_file_handle_recovery():
    """测试文件句柄恢复"""
    log("🧪 开始测试文件句柄恢复")
    
    # 模拟文件句柄被意外关闭
    try:
        if file_handler.stream:
            log("📝 关闭前的日志消息")
            file_handler.stream.close()
            log("📝 关闭后的日志消息（应该能自动恢复）")
    except Exception as e:
        log(f"⚠️ 文件句柄测试异常: {e}")
    
    log("✅ 文件句柄恢复测试完成")

def main():
    """主测试函数"""
    log("🚀 开始日志修复测试")
    log(f"📁 日志文件路径: {log_file}")
    
    # 运行各项测试
    test_normal_logging()
    test_exception_logging()
    test_multithread_logging()
    test_file_handle_recovery()
    
    log("🎉 所有测试完成！")
    log(f"📊 请检查日志文件: {log_file}")

if __name__ == "__main__":
    main()
