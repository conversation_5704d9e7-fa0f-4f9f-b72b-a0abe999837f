﻿[2025-07-22 12:05:04,411] 🚀 开始日志修复测试
[2025-07-22 12:05:04,411] 📁 日志文件路径: D:\hubtest\测试日志修复_20250722_120504.log
[2025-07-22 12:05:04,411] 🧪 开始测试正常日志记录
[2025-07-22 12:05:04,411] 📝 正常日志消息 1
[2025-07-22 12:05:04,514] 📝 正常日志消息 2
[2025-07-22 12:05:04,620] 📝 正常日志消息 3
[2025-07-22 12:05:04,728] 📝 正常日志消息 4
[2025-07-22 12:05:04,836] 📝 正常日志消息 5
[2025-07-22 12:05:04,944] ✅ 正常日志记录测试完成
[2025-07-22 12:05:04,944] 🧪 开始测试异常日志记录
[2025-07-22 12:05:04,944] ⚠️ 捕获到异常: 这是一个测试异常
[2025-07-22 12:05:04,944] ✅ 异常日志记录测试完成
[2025-07-22 12:05:04,945] 🧪 开始测试多线程日志记录
[2025-07-22 12:05:04,946] 🔄 线程1 消息1
[2025-07-22 12:05:04,947] 🔄 线程2 消息1
[2025-07-22 12:05:04,947] 🔄 线程3 消息1
[2025-07-22 12:05:05,005] 🔄 线程3 消息2
[2025-07-22 12:05:05,005] 🔄 线程1 消息2
[2025-07-22 12:05:05,005] 🔄 线程2 消息2
[2025-07-22 12:05:05,066] 🔄 线程1 消息3
[2025-07-22 12:05:05,066] 🔄 线程3 消息3
[2025-07-22 12:05:05,066] 🔄 线程2 消息3
[2025-07-22 12:05:05,129] ✅ 多线程日志记录测试完成
[2025-07-22 12:05:05,130] 🧪 开始测试文件句柄恢复
[2025-07-22 12:05:05,130] 📝 关闭前的日志消息
[2025-07-22 12:05:05,130] 📝 关闭后的日志消息（应该能自动恢复）
[2025-07-22 12:05:05,131] ✅ 文件句柄恢复测试完成
[2025-07-22 12:05:05,131] 🎉 所有测试完成！
[2025-07-22 12:05:05,131] 📊 请检查日志文件: D:\hubtest\测试日志修复_20250722_120504.log
