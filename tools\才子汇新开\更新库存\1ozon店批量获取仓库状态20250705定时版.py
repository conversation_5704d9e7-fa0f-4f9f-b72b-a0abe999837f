import os
os.environ.pop("HTTP_PROXY", None)
os.environ.pop("HTTPS_PROXY", None)
os.environ["NO_PROXY"] = "*"

import schedule
import pandas as pd
import requests
import time
import random
import logging
import io
import sys
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
import queue
import threading
# ========== 配置参数 ==========
input_file = r'D:\gx\查询状态上库存归档报活动店铺表格\查询商品状态数量统计-上库存-归档-20250426.xlsx'
log_dir = r'D:\gx'
use_proxy = False
retries = 3
MAX_WORKERS = 20
# =============================

# ========== 日志配置函数 ==========
def setup_logger(timestamp):
    log_file = fr'{log_dir}\仓库状态日志_{timestamp}.log'
    sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
    logger = logging.getLogger()
    logger.setLevel(logging.INFO)
    logger.handlers.clear()

    file_handler = logging.FileHandler(log_file, encoding='utf-8-sig')
    file_handler.setFormatter(logging.Formatter('[%(asctime)s] %(message)s'))

    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(logging.Formatter('[%(asctime)s] %(message)s'))

    logger.handlers = [file_handler, console_handler]
    return logger

# ========== 写入线程 ==========
def writer_thread_fn(result_queue, output_file, logger):
    def log(msg): logger.info(msg)
    buffer = []
    flush_interval = 1
    last_flush_time = time.time()

    while True:
        try:
            result = result_queue.get(timeout=1)
            if result is None:
                break
            buffer.extend(result) if isinstance(result, list) else buffer.append(result)
            now = time.time()
            if now - last_flush_time >= flush_interval:
                if buffer:
                    try:
                        new_df = pd.DataFrame(buffer)
                        if os.path.exists(output_file):
                            old_df = pd.read_excel(output_file)
                        else:
                            old_df = pd.DataFrame()
                        final_df = pd.concat([old_df, new_df], ignore_index=True)
                        final_df.to_excel(output_file, index=False)
                        log(f"💾 已批量保存 {len(buffer)} 条记录到：{output_file}")
                    except Exception as e:
                        log(f"❗ 写入失败：{e}")
                    buffer.clear()
                    last_flush_time = now
            result_queue.task_done()
        except queue.Empty:
            now = time.time()
            if buffer and now - last_flush_time >= flush_interval:
                try:
                    new_df = pd.DataFrame(buffer)
                    if os.path.exists(output_file):
                        old_df = pd.read_excel(output_file)
                    else:
                        old_df = pd.DataFrame()
                    final_df = pd.concat([old_df, new_df], ignore_index=True)
                    final_df.to_excel(output_file, index=False)
                    log(f"💾 已批量保存 {len(buffer)} 条记录到：{output_file}")
                except Exception as e:
                    log(f"❗ 写入失败：{e}")
                buffer.clear()
                last_flush_time = now

# ========== 单次任务执行函数 ==========
def run_all_environments():
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    output_file = fr'{log_dir}\仓库状态统计_{timestamp}.xlsx'
    logger = setup_logger(timestamp)

    def log(msg): logger.info(msg)

    result_queue = queue.Queue()

    def process_environment(row):
        environment_name = row['环境名']
        client_id = str(row['ID']).replace('.0', '')
        api_key = row['授权码']

        headers = {
            "Client-Id": client_id,
            "Api-Key": api_key
        }

        proxies = {
            "http": "http://127.0.0.1:7890",
            "https": "http://127.0.0.1:7890"
        } if use_proxy else None

        log(f"\n🌍 开始处理环境: {environment_name}")
        for attempt in range(retries):
            try:
                response = requests.post(
                    'https://api-seller.ozon.ru/v1/warehouse/list',
                    headers=headers,
                    json={},
                    proxies=proxies,
                    timeout=10
                )
                log(f"⬅️ Status Code: {response.status_code}，环境: {environment_name}")
                if response.status_code == 200:
                    result = response.json().get("result", [])
                    row_list = []
                    for wh in result:
                        row_list.append({
                            "环境名": environment_name,
                            "warehouse_id": wh.get("warehouse_id"),
                            "name": wh.get("name"),
                            "status": wh.get("status")
                        })
                    result_queue.put(row_list)
                    return environment_name
                else:
                    log(f"❌ 请求失败，状态码: {response.status_code}，环境: {environment_name}")
            except Exception as e:
                log(f"❗ 网络异常: {e}，环境: {environment_name}")
            time.sleep(1)
        log(f"⚠️ 所有重试失败，跳过环境: {environment_name}")
        return environment_name

    df = pd.read_excel(input_file)
    df = df.dropna(subset=['ID', '授权码'])
    total_envs = len(df)
    start_time = time.time()

    writer = threading.Thread(target=writer_thread_fn, args=(result_queue, output_file, logger))
    writer.start()

    with ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
        futures = []
        for _, row in df.iterrows():
            futures.append(executor.submit(process_environment, row))

        for future in as_completed(futures):
            try:
                env = future.result()
                log(f"✅ 完成环境: {env}")
            except Exception as e:
                log(f"❌ 异常环境处理: {e}")

    result_queue.put(None)
    writer.join()

    # ✅ 修复日志写入线程退出后继续写入导致的 ValueError 问题
    for handler in logger.handlers:
        handler.close()
        logger.removeHandler(handler)

    end_time = time.time()
    elapsed = end_time - start_time
    d, rem = divmod(elapsed, 86400)
    h, rem = divmod(rem, 3600)
    m, s = divmod(rem, 60)

    log(f"\n✅ 所有仓库状态查询完成！已保存到：{output_file}")
    log(f"🌍 总环境数：{total_envs}")
    log(f"⏱️ 总耗时：{int(d)}天 {int(h)}小时 {int(m)}分钟 {int(s)}秒")


# ========== 定时调度 ==========
if __name__ == "__main__":
    # 每天固定时间调度
    schedule.every().day.at("12:20").do(run_all_environments)
    # schedule.every().day.at("12:00").do(run_all_environments)
    # schedule.every().day.at("16:00").do(run_all_environments)
    schedule.every().day.at("20:00").do(run_all_environments)

    print("📅 定时任务启动中... 等待执行时间点到达")

    while True:
        schedule.run_pending()
        time.sleep(5)
