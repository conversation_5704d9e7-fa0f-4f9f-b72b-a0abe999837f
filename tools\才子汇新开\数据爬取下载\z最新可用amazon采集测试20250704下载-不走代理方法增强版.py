# -*- coding: utf-8 -*-
import os
os.environ.pop("HTTP_PROXY", None)
os.environ.pop("HTTPS_PROXY", None)
os.environ["NO_PROXY"] = "*"

import re
import time
import random
import pandas as pd
import requests
from datetime import datetime


# === 配置区域 ===
input_file = r"D:\待处理\还没处理-采集挂毯详情页amazon_detail_sku_results_20250720_223854.xlsx"  # 请修改为你的 Excel 路径
output_dir = "images-amazon-20250721搜索词gt"                                  # 输出图片根目录

use_proxy = False
force_no_proxy = True

proxies = {
    "http":  "http://127.0.0.1:7890",
    "https": "http://127.0.0.1:7890",
}

# === 日志与失败链接文件 ===
timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
log_file  = f"amazon_download_log_{timestamp}.log"
fail_file = f"amazon_failed_links_{timestamp}.txt"

def log(msg):
    print(msg)
    with open(log_file, "a", encoding="utf-8-sig") as f:
        f.write(msg + "\n")

# 捕获 URL 最后一段文件名
filename_pattern = re.compile(r".*/([^/?]+)$")

# 内置 UA 列表
USER_AGENTS = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; WOW64; rv:113.0) Gecko/20100101 Firefox/113.0",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Safari/605.1.15",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Mozilla/5.0 (iPhone; CPU iPhone OS 17_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1",
]

# 加载 Excel
df = pd.read_excel(input_file)
# 保留 搜索词 和 大图链接 两列，删除空值
df = df[["搜索词", "大图链接"]].dropna(subset=["搜索词", "大图链接"])
total = len(df)

if total == 0:
    log("❌ 没有有效数据，检查输入文件")
    exit(1)

os.makedirs(output_dir, exist_ok=True)
failed_links = []

for idx, row in df.iterrows():
    search    = str(row["搜索词"]).strip()
    img_url   = str(row["大图链接"]).strip()
    current   = idx + 1
    percent   = round(current / total * 100, 2)

    m = filename_pattern.match(img_url)
    if not m:
        log(f"[{current}/{total}][{percent}%] URL 格式不匹配，跳过: {img_url}")
        continue

    orig_name = m.group(1)                          # e.g. 81QL2U62wmL._AC_SL1500_.jpg
    name_base, _ = os.path.splitext(orig_name)
    filename = f"{name_base}.png"                   # 统一改为 png

    # 创建以 搜索词 命名的子目录
    safe_folder = re.sub(r'[\\/:\*\?"<>\|]', '_', search)
    folder_path = os.path.join(output_dir, safe_folder)
    os.makedirs(folder_path, exist_ok=True)
    outpath = os.path.join(folder_path, filename)

    if os.path.exists(outpath):
        log(f"[{current}/{total}][{percent}%] ✅ 已存在，跳过: {filename}")
        continue

    # 动态 sleep，模拟人类行为
    time.sleep(random.uniform(2, 5))

    headers = {
        "User-Agent": random.choice(USER_AGENTS)
    }
    success = False

    for attempt in range(3):
        try:
            if force_no_proxy:
                resp = requests.get(img_url, headers=headers, proxies={}, timeout=15)
            elif use_proxy:
                resp = requests.get(img_url, headers=headers, proxies=proxies, timeout=15)
            else:
                resp = requests.get(img_url, headers=headers, timeout=15)

            if resp.status_code == 200:
                with open(outpath, 'wb') as f:
                    f.write(resp.content)
                log(f"[{current}/{total}][{percent}%] ✅ 下载成功: {outpath}")
                success = True
                break
            else:
                log(f"[{current}/{total}][{percent}%] ❌ 状态码 {resp.status_code}，尝试 {attempt+1}/3: {img_url}")
        except Exception as e:
            log(f"[{current}/{total}][{percent}%] ⚠️ 错误 {attempt+1}/3: {img_url}")
            log(f"    ↳ {e}")
        # 重试前稍等
        time.sleep(1 + random.uniform(0.5, 1.5) * (attempt + 1))

    if not success:
        failed_links.append(img_url)
        log(f"[{current}/{total}][{percent}%] ❌ 最终失败: {img_url}")

log(f"\n🎉 全部完成，共处理 {total} 条，大图下载失败 {len(failed_links)} 条")

if failed_links:
    with open(fail_file, "w", encoding="utf-8-sig") as f:
        for link in failed_links:
            f.write(link + "\n")
    log(f"❗ 失败链接已记录到: {fail_file}")
